import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useUser } from '../context/UserContext';
import { FaEye, FaEyeSlash } from 'react-icons/fa';

export default function Login() {
  const router = useRouter();
  const { login, loading, isAuthenticated, user } = useUser();
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    // Don't do anything while the auth state is loading
    if (loading) {
      return;
    }
    // If the user is authenticated, redirect them
    if (isAuthenticated()) {
      router.push('/dashboard');
    }
  }, [user, loading, router, isAuthenticated]);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    try {
      console.log("Attempting login with:", formData);
      const user = await login(formData);
      console.log("Login response:", user);
      
      if (user) {
        console.log("Login successful, token stored:", localStorage.getItem('token'));
        router.push('/dashboard');
      } else {
        console.log("Login failed");
        setError('Invalid username or password');
      }
    } catch (err) {
      console.error("Login error:", err.message);
      // Check for the specific inactive account message from the API
      if (err.message === 'user account is inactive') {
        setError('Akun Anda telah dinonaktifkan. Silakan hubungi administrator.');
      } else if (err.message === 'Invalid credentials') {
        setError('Username atau password salah');
      } else {
        // Display other error messages from the API, or a generic one
        setError(err.message || 'Invalid username or password');
      }
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      position: 'relative',
      fontFamily: 'Arial, sans-serif',
      backgroundImage: "url('setengah.png')",
      backgroundSize: 'cover',
      backgroundPosition: 'center',
    }}>
      {/* Form overlay positioned on the right */}
      <div style={{
        position: 'absolute',
        top: '0',
        right: '0',
        width: '650px',
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '2rem',
      }}>
        <div style={{
          // --- MODIFIKASI VISUAL ---
          backgroundColor: 'rgba(255, 255, 255, 0.85)', // Dulu 'white', sekarang semi-transparan
          backdropFilter: 'blur(10px)', // EFEK KACA: Membuat background di belakangnya blur
          border: '1px solid rgba(255, 255, 255, 0.4)', // Tambahan: Garis tepi halus untuk mempertegas bentuk di atas background
          // --- AKHIR MODIFIKASI VISUAL ---

          padding: '2.5rem',
          borderRadius: '15px', // Diperbesar sedikit dari '10px' agar lebih lembut
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)', // Dibuat lebih menyebar dan halus
          width: '100%',
          maxWidth: '380px',
        }}>
          <h1 style={{
            textAlign: 'center',
            color: '#333',
            marginBottom: '2rem',
            fontSize: '1.8rem',
            fontWeight: '600', // Ditambah agar judul lebih tegas
          }}>
            Welcome Back
          </h1>

          {error && (
            <div style={{
              backgroundColor: '#fee2e2',
              color: '#dc2626',
              padding: '0.75rem',
              borderRadius: '5px',
              marginBottom: '1rem',
              fontSize: '0.875rem',
            }}>
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div style={{ marginBottom: '1.5rem' }}>
              <label
                htmlFor="username"
                style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  color: '#4b5563',
                  fontSize: '0.875rem',
                }}
              >
                Username
              </label>
              <input
                type="text"
                id="username"
                name="username"
                value={formData.username}
                onChange={handleChange}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  borderRadius: '5px',
                  border: '1px solid #d1d5db',
                  fontSize: '0.875rem',
                  outline: 'none',
                  transition: 'border-color 0.2s',
                }}
              />
            </div>

            <div style={{ marginBottom: '1.5rem' }}>
              <label
                htmlFor="password"
                style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  color: '#4b5563',
                  fontSize: '0.875rem',
                }}
              >
                Password
              </label>
              <div style={{ position: 'relative' }}>
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    paddingRight: '2.5rem', // Make space for the icon
                    borderRadius: '5px',
                    border: '1px solid #d1d5db',
                    fontSize: '0.875rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                  }}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  style={{
                    position: 'absolute',
                    right: '0.5rem',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    background: 'transparent',
                    border: 'none',
                    cursor: 'pointer',
                    color: '#6b7280',
                    fontSize: '1.1rem'
                  }}
                >
                  {showPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              style={{
                width: '100%',
                padding: '0.75rem',
                backgroundColor: '#16a34a',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.7 : 1,
                transition: 'background-color 0.2s',
              }}
            >
              {loading ? 'Sedang Login...' : 'Login'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}