import React, { useEffect, useState } from "react";
import {
    Panel,
    Stack,
    IconButton,
    InputGroup,
    Input,
    Table,
    Pagination,
    Modal,
    Form,
    Button,
    useToaster,
    SelectPicker,
    Notification,
} from "rsuite";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import { useRouter } from "next/router";
import ApiMasterdataUsers from "@/pages/api/masterdata_users/masterdata_users";
import ApiMasterdataRoles from "@/pages/api/masterdata_roles/masterdata_roles";
import ApiAuth from "../api/auth/auth";
import { useUser } from "@/context/UserContext";
import ContainerLayout from "@/components/layout/ContainerLayout";
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';

function MasterdataUsers() {
    const { HeaderCell, Cell, Column } = Table;
    const [searchKeyword, setSearchKeyword] = useState("");
    const toaster = useToaster();
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const router = useRouter();
    const { user, isAuthenticated, loading } = useUser();

    const [usersDataState, setUsersDataState] = useState([]);
    const [rolesDataState, setRolesDataState] = useState([]);

    const emptyUserForm = {
        "username": null,
        "email": null,
        "password": null,
        "role_id": null,
        "no_karyawan": null,
    };

    const [showAddModal, setShowAddModal] = useState(false);
    const [addUserForm, setAddUserForm] = useState(emptyUserForm);
    const [errorsAddForm, setErrorsAddForm] = useState({});
    const [showEditModal, setShowEditModal] = useState(false);
    const [editLoading, setEditLoading] = useState(false);
    const [editUserForm, setEditUserForm] = useState(emptyUserForm);
    const [errorsEditForm, setErrorsEditForm] = useState({});
    const [addLoading, setAddLoading] = useState(false);

    const showNotification = (type, message, duration = 2000) => {
        toaster.push(
            <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
                <p>{message}</p>
            </Notification>,
            { placement: "topEnd", duration }
        );
    };

    // Functions for handling search, pagination, and sorting
    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    // Filter data based on search keyword
    const filteredData = usersDataState.filter((rowData) => {
        const searchFields = [
            "id",
            "username",
            "email",
            "role_name",
            "no_karyawan",
            "created_at",
            "updated_at",
            "status",
        ];

        return searchFields.some((field) =>
            rowData[field]
                ?.toString()
                .toLowerCase()
                .includes(searchKeyword.toLowerCase())
        );
    });

    // Get paginated data
    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    };

    // Sort data
    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword
        ? filteredData.length
        : usersDataState.length;

    useEffect(() => {
        if (loading) return;
        if (!isAuthenticated()) {
            router.push("/login");
            return;
        }

        HandleGetAllUsersApi();
        HandleGetAllRolesApi();
    }, [user, loading]);

    const HandleGetAllUsersApi = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log("Token before API call HandleGetAllUsersApi:", token);

            const res = await ApiMasterdataUsers().getAllUsers();

            console.log("API response:", res);
            if (res.status === 200) {
                setUsersDataState(res.data);
            } else {
                console.log("error on GetAllUsersApi", res.message);
                showNotification("error", "Error fetching users: " + res.message);

                if (res.status === 401) {
                    showNotification("error", "Session expired. Please log in again.");
                    router.push("/login");
                }
            }
        } catch (error) {
            console.log("error on catch GetAllUsersApi", error);
            showNotification("error", "Error fetching users: " + error.message);
        }
    };

    const HandleGetAllRolesApi = async () => {
        try {
            const res = await ApiMasterdataRoles().getAllRoles();
            if (res.status === 200) {
                setRolesDataState(res.data);
            } else {
                console.log("error on GetAllRolesApi", res.message);
            }
        } catch (error) {
            console.log("error on catch GetAllRolesApi", error);
        }
    };

    const HandleAddUserApi = async () => {
        const errors = {};

        if (!addUserForm.username) errors.username = "Username is required";
        if (!addUserForm.email) errors.email = "Email is required";
        if (!addUserForm.password) errors.password = "Password is required";
        if (!addUserForm.role_id) errors.role_id = "Role is required";

        if (Object.keys(errors).length > 0) {
            setErrorsAddForm(errors);
            return;
        }

        setAddLoading(true);
        try {
            const res = await ApiAuth().Register(addUserForm);

            if (res.status === 200) {
                showNotification("success", "User added successfully");
                setShowAddModal(false);
                setAddUserForm(emptyUserForm);
                HandleGetAllUsersApi();
            } else {
                showNotification("error", "Error adding user: " + res.message);
            }
        } catch (error) {
            showNotification("error", "Error adding user: " + error.message);
        } finally {
            setAddLoading(false);
        }
    };

    const HandleEditUserApi = async () => {
        const errors = {};

        if (!editUserForm.username) errors.username = "Username is required";
        if (!editUserForm.email) errors.email = "Email is required";
        if (!editUserForm.role_id) errors.role_id = "Role is required";

        if (Object.keys(errors).length > 0) {
            setErrorsEditForm(errors);
            return;
        }

        setEditLoading(true);
        try {
            const res = await ApiMasterdataUsers().updateUser(editUserForm);

            if (res.status === 200) {
                showNotification("success", "User updated successfully");
                setShowEditModal(false);
                setEditUserForm(emptyUserForm);
                HandleGetAllUsersApi();
            } else {
                showNotification("error", "Error updating user: " + res.message);
            }
        } catch (error) {
            showNotification("error", "Error updating user: " + error.message);
        } finally {
            setEditLoading(false);
        }
    };

    const handleEditStatus = async (id) => {
        try {
            const user = usersDataState.find(u => u.id === id);
            const newStatus = user.status === 0 ? 1 : 0;

            const res = await ApiMasterdataUsers().updateUserStatus({
                id: id,
                status: newStatus
            });

            if (res.status === 200) {
                showNotification("success", `User ${newStatus === 1 ? 'activated' : 'deactivated'} successfully`);
                HandleGetAllUsersApi();
            } else {
                showNotification("error", "Error updating user status: " + res.message);
            }
        } catch (error) {
            showNotification("error", "Error updating user status: " + error.message);
        }
    };

    const handleEditClick = (rowData) => {
        setEditUserForm({
            id: rowData.id,
            username: rowData.username,
            email: rowData.email,
            role_id: rowData.role_id,
            no_karyawan: rowData.no_karyawan,
            password: ""
        });
        setShowEditModal(true);
    };

    return (
        <ContainerLayout title="Masterdata Users">
            <div>
                <Panel
                    bordered
                    bodyFill
                    className="mb-3"
                    header={
                        <Stack justifyContent="space-between">
                            <h5>Halaman tambah pengguna</h5>
                        </Stack>
                    }
                ></Panel>
                <Panel
                    bordered
                    bodyFill
                    header={
                        <Stack justifyContent="space-between">
                            <div className="flex gap-2">
                                <IconButton
                                    icon={<PlusRoundIcon />}
                                    appearance="primary"
                                    onClick={() => {
                                        setShowAddModal(true);
                                    }}
                                >
                                    Add
                                </IconButton>
                            </div>

                            <InputGroup inside>
                                <InputGroup.Addon>
                                    <SearchIcon />
                                </InputGroup.Addon>
                                <Input
                                    placeholder="search"
                                    value={searchKeyword}
                                    onChange={handleSearch}
                                />
                                <InputGroup.Addon
                                    onClick={() => {
                                        setSearchKeyword("");
                                        setPage(1);
                                    }}
                                    style={{
                                        display: searchKeyword ? "block" : "none",
                                        color: "red",
                                        cursor: "pointer",
                                    }}
                                >
                                    <CloseOutlineIcon />
                                </InputGroup.Addon>
                            </InputGroup>
                        </Stack>
                    }
                >
                    <Table
                        bordered
                        cellBordered
                        height={400}
                        data={getPaginatedData(getFilteredData(), limit, page)}
                        sortColumn={sortColumn}
                        sortType={sortType}
                        onSortColumn={handleSortColumn}
                    >
                        <Column width={70} align='center' fixed>
                            <HeaderCell>No</HeaderCell>
                            <Cell>
                                {(rowData, index) => {
                                    return index + 1 + limit * (page - 1);
                                }}
                            </Cell>
                        </Column>
                        <Column width={150} sortable>
                            <HeaderCell align="center">Employee No.</HeaderCell>
                            <Cell dataKey="no_karyawan" />
                        </Column>
                        <Column width={150} sortable>
                            <HeaderCell align="center">Username</HeaderCell>
                            <Cell dataKey="username" />
                        </Column>
                        <Column width={200} sortable>
                            <HeaderCell align="center">Email</HeaderCell>
                            <Cell dataKey="email" />
                        </Column>
                        <Column width={150} sortable>
                            <HeaderCell align="center">Role</HeaderCell>
                            <Cell dataKey="role_name" />
                        </Column>
                        <Column width={150} sortable resizable align="center">
                            <HeaderCell>Created Date</HeaderCell>
                            <Cell>
                                {(rowData) =>
                                    new Date(rowData.created_at).toLocaleDateString("en-GB")
                                }
                            </Cell>
                        </Column>
                        <Column width={150} sortable resizable align="center">
                            <HeaderCell>Updated Date</HeaderCell>
                            <Cell>
                                {(rowData) =>
                                    rowData.updated_at ? new Date(rowData.updated_at).toLocaleDateString("en-GB") : ''
                                }
                            </Cell>
                        </Column>
                        <Column width={100} sortable resizable align="center">
                            <HeaderCell>Status</HeaderCell>
                            <Cell>
                                {(rowData) => (
                                    <span
                                        style={{
                                            color: rowData.status === 1 ? "green" : "red",
                                        }}
                                    >
                                        {rowData.status === 1 ? "Active" : "Inactive"}
                                    </span>
                                )}
                            </Cell>
                        </Column>
                        <Column width={120} fixed="right" align="center">
                            <HeaderCell>Action</HeaderCell>
                            <Cell style={{ padding: "8px" }}>
                                {(rowData) => (
                                    <div>
                                        <Button
                                            appearance="link"
                                            disabled={rowData.status === 0}
                                            onClick={() => handleEditClick(rowData)}
                                        >
                                            Edit
                                        </Button>
                                        <Button
                                            appearance="subtle"
                                            onClick={() => handleEditStatus(rowData.id)}
                                        >
                                            {rowData.status === 1 ? (
                                                <TrashIcon style={{ fontSize: "16px" }} />
                                            ) : (
                                                <ReloadIcon style={{ fontSize: "16px" }} />
                                            )}
                                        </Button>
                                    </div>
                                )}
                            </Cell>
                        </Column>
                    </Table>

                    <div style={{ padding: 20 }}>
                        <Pagination
                            prev
                            next
                            first
                            last
                            ellipsis
                            boundaryLinks
                            maxButtons={5}
                            size="xs"
                            layout={["total", "-", "limit", "|", "pager", "skip"]}
                            limitOptions={[10, 30, 50]}
                            total={totalRowCount}
                            limit={limit}
                            activePage={page}
                            onChangePage={setPage}
                            onChangeLimit={handleChangeLimit}
                        />
                    </div>
                </Panel>

                {/* Add User Modal */}
                <Modal
                    backdrop="static"
                    open={showAddModal}
                    onClose={() => {
                        setShowAddModal(false);
                        setAddUserForm(emptyUserForm);
                        setErrorsAddForm({});
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Add User</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Username</Form.ControlLabel>
                                <Form.Control
                                    name="username"
                                    value={addUserForm.username}
                                    onChange={(value) => {
                                        setAddUserForm((prev) => ({
                                            ...prev,
                                            username: value,
                                        }));
                                        setErrorsAddForm((prev) => ({
                                            ...prev,
                                            username: undefined,
                                        }));
                                    }}
                                />
                                {errorsAddForm.username && <p style={{ color: 'red' }}>{errorsAddForm.username}</p>}
                            </Form.Group>
                            <Form.Group>

                                <Form.ControlLabel>Email</Form.ControlLabel>
                                <Form.Control
                                    name="email"
                                    value={addUserForm.email}
                                    onChange={(value) => {
                                        setAddUserForm((prev) => ({
                                            ...prev,
                                            email: value,
                                        }));
                                        setErrorsAddForm((prev) => ({
                                            ...prev,
                                            email: undefined,
                                        }));
                                    }}
                                />
                                {errorsAddForm.email && <p style={{ color: 'red' }}>{errorsAddForm.email}</p>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Password</Form.ControlLabel>
                                <Form.Control
                                    name="password"
                                    type="password"
                                    value={addUserForm.password}
                                    onChange={(value) => {
                                        setAddUserForm((prev) => ({
                                            ...prev,
                                            password: value,
                                        }));
                                        setErrorsAddForm((prev) => ({
                                            ...prev,
                                            password: undefined,
                                        }));
                                    }}
                                />
                                {errorsAddForm.password && <p style={{ color: 'red' }}>{errorsAddForm.password}</p>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Role</Form.ControlLabel>
                                <SelectPicker
                                    data={rolesDataState.map(role => ({
                                        label: role.role_name,
                                        value: role.id
                                    }))}
                                    value={addUserForm.role_id}
                                    onChange={(value) => {
                                        setAddUserForm((prev) => ({
                                            ...prev,
                                            role_id: value,
                                        }));
                                        setErrorsAddForm((prev) => ({
                                            ...prev,
                                            role_id: undefined,
                                        }));
                                    }}
                                    block
                                />
                                {errorsAddForm.role_id && <p style={{ color: 'red' }}>{errorsAddForm.role_id}</p>}
                            </Form.Group>

                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowAddModal(false);
                                setAddUserForm(emptyUserForm);
                                setErrorsAddForm({});
                            }}
                            appearance="subtle"
                            disabled={addLoading}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={HandleAddUserApi}
                            appearance="primary"
                            loading={addLoading}
                            disabled={addLoading}
                            type="submit"
                        >
                            Add
                        </Button>
                    </Modal.Footer>
                </Modal>

                {/* Edit User Modal */}
                <Modal
                    backdrop="static"
                    open={showEditModal}
                    onClose={() => {
                        setShowEditModal(false);
                        setEditUserForm(emptyUserForm);
                        setErrorsEditForm({});
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Edit User</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Username</Form.ControlLabel>
                                <Form.Control
                                    name="username"
                                    value={editUserForm.username}
                                    onChange={(value) => {
                                        setEditUserForm((prev) => ({
                                            ...prev,
                                            username: value,
                                        }));
                                        setErrorsEditForm((prev) => ({
                                            ...prev,
                                            username: undefined,
                                        }));
                                    }}
                                />
                                {errorsEditForm.username && <p style={{ color: 'red' }}>{errorsEditForm.username}</p>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Email</Form.ControlLabel>
                                <Form.Control
                                    name="email"
                                    value={editUserForm.email}
                                    onChange={(value) => {
                                        setEditUserForm((prev) => ({
                                            ...prev,
                                            email: value,
                                        }));
                                        setErrorsEditForm((prev) => ({
                                            ...prev,
                                            email: undefined,
                                        }));
                                    }}
                                />
                                {errorsEditForm.email && <p style={{ color: 'red' }}>{errorsEditForm.email}</p>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Role</Form.ControlLabel>
                                <SelectPicker
                                    data={rolesDataState.map(role => ({
                                        label: role.role_name,
                                        value: role.id
                                    }))}
                                    value={editUserForm.role_id}
                                    onChange={(value) => {
                                        setEditUserForm((prev) => ({
                                            ...prev,
                                            role_id: value,
                                        }));
                                        setErrorsEditForm((prev) => ({
                                            ...prev,
                                            role_id: undefined,
                                        }));
                                    }}
                                    block
                                />
                                {errorsEditForm.role_id && <p style={{ color: 'red' }}>{errorsEditForm.role_id}</p>}
                            </Form.Group>

                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowEditModal(false);
                                setEditUserForm(emptyUserForm);
                                setErrorsEditForm({});
                            }}
                            appearance="subtle"
                            disabled={editLoading}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={HandleEditUserApi}
                            appearance="primary"
                            loading={editLoading}
                            disabled={editLoading}
                            type="submit"
                        >
                            Update
                        </Button>
                    </Modal.Footer>
                </Modal>
            </div>
        </ContainerLayout>
    );
}

export default withRoleAccess(MasterdataUsers, rolePermissions['masterdata-users']);