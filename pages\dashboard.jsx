import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Panel } from 'rsuite';
import ContainerLayout from '@/components/layout/ContainerLayout';
import { useUser } from '../context/UserContext';
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';

function Dashboard() {
    const router = useRouter();
    const { user, loading } = useUser();
    const [currentTime, setCurrentTime] = useState(new Date());

    // Update the time every second
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);

        // Clean up the interval on component unmount
        return () => clearInterval(timer);
    }, []);

    if (loading || !user) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="flex flex-col items-center space-y-4">
                    <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
                    <p className="text-gray-600 font-medium">Loading your dashboard...</p>
                </div>
            </div>
        );
    }

    const getCurrentTime = () => {
        const now = new Date();
        const hour = now.getHours();
        if (hour < 12) return "Good Morning";
        if (hour < 17) return "Good Afternoon";
        return "Good Evening";
    };

    // Format the current time
    const formatTime = (date) => {
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
    };

    // Format the current date
    const formatDate = (date) => {
        return date.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const handleActionClick = (path) => {
        if (path) {
            router.push(path);
        }
    };

    // Define quick actions based on user role
    const roleSpecificActions = {
        1: [ // Role 1: Admin
            { title: "Manajemen Role", icon: "➕", color: "bg-green-500", path: "/masterdata-roles" },
            { title: "Manajemen Karyawan", icon: "👥", color: "bg-blue-500", path: "masterdata-users" },

        ],
        2: [ // Role 2: Data Entry / Product Manager
            { title: "Manajemen PPR", icon: "📝", color: "bg-purple-500", path: "/sisko/masterdata/ppr" },
            { title: "Tambah Indikator", icon: "➕", color: "bg-orange-500", path: "/sisko/masterdata/indikator" }
        ],
        3: [ // Role 3: Approver / Manager
            { title: "Persetujuan PPR", icon: "✅", color: "bg-teal-500", path: "/sisko/approval/ppr" },
            { title: "Riwayat Persetujuan", icon: "📜", color: "bg-indigo-500", path: "/sisko/approval/reporting" }
        ],
        4: [ // Role 4: Transaction User
            { title: "Buat Transaksi", icon: "➕", color: "bg-red-500", path: "/sisko/operator/creation/addTransactionHeader" },
            { title: "Persetujuan Transaksi", icon: "📋", color: "bg-yellow-500", path: "/sisko/operator/reporting" }
        ],
        // Default actions if role_id is not found or for other roles
        default: [
            { title: "Lihat Dashboard", icon: "📊", color: "bg-blue-500", path: "/dashboard" },
            { title: "Hubungi Bantuan", icon: "📞", color: "bg-gray-500", path: "/support" }
        ]
    };

    const quickActions = roleSpecificActions[user.role_id] || roleSpecificActions.default;


    const machineData = [
        { name: "Machine Drying", icon: "🔥", status: "Disconnected" },
        { name: "Machine Sifting", icon: "🌪️", status: "Disconnected" },
        { name: "Machine Mixing", icon: "🔄", status: "Disconnected" },
        { name: "Machine Granule", icon: "⚗️", status: "Disconnected" },
        { name: "Machine Final Mixing", icon: "🌀", status: "Disconnected" }
    ];

    return (
        <ContainerLayout>
            <div className="space-y-6 mx-4 md:mx-6 lg:mx-8">
                {/* Welcome Section */}
                <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 text-white shadow-xl">
                    <div className="flex flex-col md:flex-row items-start md:items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold mb-2">
                                {getCurrentTime()}, {user.username}! 👋
                            </h1>
                            <p className="text-green-100 text-lg mb-4">
                                Welcome back to your dashboard. Here's what's happening today.
                            </p>
                        </div>

                        <div className="flex flex-row md:flex-col items-center mt-4 md:mt-0">
                            <div className="w-20 h-20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg mb-0 md:mb-3">
                                <span className="text-3xl">🧪</span>
                            </div>
                            <div className="ml-4 md:ml-0 text-center">
                                <p className="text-white text-xl font-semibold">{formatTime(currentTime)}</p>
                                <p className="text-green-100 text-sm">{formatDate(currentTime)}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Data Connectivity Section */}
                <div>
                    <h5 className="text-lg font-semibold text-gray-700 mb-4">Machine Connectivity</h5>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
                        {machineData.map((machine, index) => (
                            <div key={index} className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100 flex flex-col items-center text-center">
                                <div className="text-4xl mb-3">{machine.icon}</div>
                                <p className="text-base font-semibold text-gray-800 mb-2 h-12 flex items-center justify-center">{machine.name}</p>
                                <div className="flex items-center">
                                    <span className={`h-2.5 w-2.5 rounded-full mr-2 ${machine.status === 'Connected' ? 'bg-green-500' : 'bg-red-500'}`}></span>
                                    <p className={`text-sm font-medium ${machine.status === 'Connected' ? 'text-gray-700' : 'text-red-600'}`}>{machine.status}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Main Content Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* User Information Card */}
                    <div className="lg:col-span-2">
                        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                                    <span className="mr-2">👤</span>
                                    User Information
                                </h3>
                            </div>
                            <div className="p-6">
                                <div className="space-y-4">
                                    {[
                                        { label: "Username", value: user.username, icon: "🏷️" },
                                        { label: "Employee ID", value: user.no_karyawan, icon: "🆔" },
                                        { label: "Email", value: user.email, icon: "📧" },
                                        { label: "Role ID", value: user.role_id, icon: "🔑" }
                                    ].map((item, index) => (
                                        <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                            <div className="flex items-center space-x-3">
                                                <span className="text-xl">{item.icon}</span>
                                                <span className="font-medium text-gray-700">{item.label}</span>
                                            </div>
                                            <span className="text-gray-900 font-semibold">{item.value}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Quick Actions Card */}
                    <div className="space-y-6">
                        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                                    <span className="mr-2">⚡</span>
                                    Quick Actions
                                </h3>
                            </div>
                            <div className="p-6">
                                <div className="space-y-3">
                                    {quickActions.map((action, index) => (
                                        <button key={index} onClick={() => handleActionClick(action.path)} className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-left cursor-pointer">
                                            <div className={`w-8 h-8 ${action.color} rounded-lg flex items-center justify-center text-white text-sm`}>
                                                {action.icon}
                                            </div>
                                            <span className="font-medium text-gray-700">{action.title}</span>
                                        </button>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Activity Feed */}
                        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                                    <span className="mr-2">📈</span>
                                    Recent Activity
                                </h3>
                            </div>
                            <div className="p-6">
                                <div className="space-y-3">
                                    {[
                                        { text: "Completed task review", time: "2 min ago", color: "bg-green-500" },
                                        { text: "Updated user profile", time: "1 hour ago", color: "bg-blue-500" },
                                        { text: "Created new project", time: "3 hours ago", color: "bg-purple-500" }
                                    ].map((activity, index) => (
                                        <div key={index} className="flex items-start space-x-3">
                                            <div className={`w-2 h-2 ${activity.color} rounded-full mt-2 flex-shrink-0`}></div>
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm text-gray-800 font-medium">{activity.text}</p>
                                                <p className="text-xs text-gray-500">{activity.time}</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </ContainerLayout>
    );
}

// Export with role access control - Dashboard is accessible by all roles
export default withRoleAccess(Dashboard, rolePermissions['dashboard']);
