import { useEffect, useState } from "react";
import Head from "next/head";
import {
    Breadcrumb,
    IconButton,
    Input,
    InputGroup,
    Pagination,
    Panel,
    Stack,
    Table,
    Tag,
    Button,
    Modal,
    Form,
    useToaster,
    ButtonGroup,
    Loader,
    InputNumber,
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import ApiMasterdataKodeProduk from "@/pages/api/sisko/masterdata_kode_produk/api_masterdata_kode_produk";
import { useRouter } from "next/router";
import { useUser } from "@/context/UserContext";
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';

function MasterdataKodeProduk() {
    const { HeaderCell, Cell, Column } = Table;
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState("id_produk");
    const [sortType, setSortType] = useState("asc");
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const router = useRouter();
    const [loading, setLoading] = useState(false);

    const { user, isAuthenticated, loading: userLoading } = useUser();

    const [moduleName, setModuleName] = useState("");
    const [kodeProdukDataState, setKodeProdukDataState] = useState([]);

    const emptyAddKodeProdukForm = {
        kode_produk: "",
        bobot_min: null,
        bobot_max: null,
        bobot_std: null,
        bobot_core_foil: null,
    };

    const emptyEditKodeProdukForm = {
        id_produk: null,
        kode_produk: "",
        bobot_min: null,
        bobot_max: null,
        bobot_std: null,
        bobot_core_foil: null,
    };

    const [showAddModal, setShowAddModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [addKodeProdukForm, setAddKodeProdukForm] = useState(emptyAddKodeProdukForm);
    const [editKodeProdukForm, setEditKodeProdukForm] = useState(emptyEditKodeProdukForm);
    const [errorsAddForm, setErrorsAddForm] = useState({});
    const [errorsEditForm, setErrorsEditForm] = useState({});
    const [addLoading, setAddLoading] = useState(false);

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = kodeProdukDataState.filter((rowData) => {
        const searchFields = ["id_produk", "kode_produk", "bobot_min", "bobot_max", "bobot_std", "bobot_core_foil", "tanggal_dibuat", "dibuat_oleh", "tanggal_diubah", "diubah_oleh", "tanggal_dihapus", "dihapus_oleh", "status"];

        const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

        return matchesSearch;
    });

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    };

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : kodeProdukDataState.length;

    useEffect(() => {
        if (userLoading) return;
        if (!isAuthenticated()) {
            router.push("/login");
            return;
        }
        // if (!user?.menu_link_code?.some((item) => item.includes("KodeProduk/masterdata"))) {
        //     router.push("/dashboard");
        //     return;
        // }
        setModuleName(user.module_name || "");
        HandleGetAllKodeProdukApi();
    }, [userLoading]);

    const HandleGetAllKodeProdukApi = async () => {
        try {
            const res = await ApiMasterdataKodeProduk().getAllMasterKodeProduk();

            console.log("res", res);
            if (res.status === 200) {
                setKodeProdukDataState(res.data);
            } else {
                console.log("error on GetAllApi ", res.message);
            }
        } catch (error) {
            console.log("error on catch GetAllApi", error);
        }
    };

    const HandleAddKodeProdukApi = async () => {
        const errors = {};

        if (!addKodeProdukForm.kode_produk || addKodeProdukForm.kode_produk.trim() === "") {
            errors.kode_produk = "Kode Produk wajib diisi";
        }

        if (!addKodeProdukForm.bobot_min) {
            errors.bobot_min = "bobot_min wajib diisi";
        }
        if (!addKodeProdukForm.bobot_max) {
            errors.bobot_max = "bobot_max wajib diisi";
        }
        if (!addKodeProdukForm.bobot_std) {
            errors.bobot_std = "bobot_std wajib diisi";
        }
        if (!addKodeProdukForm.bobot_core_foil) {
            errors.bobot_core_foil = "bobot_core_foil wajib diisi";
        }

        if (addKodeProdukForm.bobot_min !== null && addKodeProdukForm.bobot_max !== null) {
            if (addKodeProdukForm.bobot_min >= addKodeProdukForm.bobot_max) {
                errors.bobot_max = "Bobot Max harus lebih besar dari Bobot Min";
            }
        }

        // If there are any errors, set them in the state
        if (Object.keys(errors).length > 0) {
            setErrorsAddForm(errors);
            return;
        }
        setAddLoading(true);
        try {
            const res = await ApiMasterdataKodeProduk().createMasterKodeProduk({
                ...addKodeProdukForm,
                dibuat_oleh: user.no_karyawan + " - " + user.username,
            });

            if (res.status === 200) {
                setAddKodeProdukForm(emptyAddKodeProdukForm);
                setShowAddModal(false);
                HandleGetAllKodeProdukApi();
            } else {
                console.log("error on AddKodeProdukApi ", res.message);
            }
        } catch (error) {
            console.log("error on AddKodeProdukApi ", error);
        } finally {
            setAddLoading(false);
        }
    };

    const HandleEditKodeProdukApi = async () => {
        const errors = {};

        if (!editKodeProdukForm.kode_produk || editKodeProdukForm.kode_produk.trim() === "") {
            errors.kode_produk = "Kode Produk wajib diisi";
        }

        if (!editKodeProdukForm.bobot_min) {
            errors.bobot_min = "Bobot Min wajib diisi";
        }
        if (!editKodeProdukForm.bobot_max) {
            errors.bobot_max = "Bobot Max wajib diisi";
        }
        if (!editKodeProdukForm.bobot_std) {
            errors.bobot_std = "Bobot Std wajib diisi";
        }
        if (!editKodeProdukForm.bobot_core_foil) {
            errors.bobot_core_foil = "Bobot Core Foil wajib diisi";
        }

        if (editKodeProdukForm.bobot_min !== null && editKodeProdukForm.bobot_max !== null) {
            if (editKodeProdukForm.bobot_min >= editKodeProdukForm.bobot_max) {
                errors.bobot_max = "Bobot Max harus lebih besar dari Bobot Min";
            }
        }

        // If there are any errors, set them in the state
        if (Object.keys(errors).length > 0) {
            setErrorsEditForm(errors);
            return;
        }


        setAddLoading(true);

        try {
            const res = await ApiMasterdataKodeProduk().editMasterKodeProduk({
                ...editKodeProdukForm,
                diubah_oleh: user.no_karyawan + " - " + user.username,
            });

            if (res.status === 200) {
                HandleGetAllKodeProdukApi();
                setShowEditModal(false);
            } else {
                console.log("error on editKodeProdukApi ", res.message);
            }
        } catch (error) {
            console.log("error on editKodeProdukApi ", res.message);
        } finally {

            setAddLoading(false);
        }
    };

    const handleEditStatusKodeProdukApi = async (id_produk, status) => {
        try {
            const res = await ApiMasterdataKodeProduk().editStatusMasterKodeProduk({
                id_produk,
                status,
                dihapus_oleh: user.no_karyawan + " - " + user.username,
            });

            if (res.status === 200) {
                console.log("Status update success:", res.message);
                HandleGetAllKodeProdukApi();
            } else {
                console.log("Error on update status: ", res.message);
            }
        } catch (error) {
            console.log("Error on update status: ", error.message);
        }
    };

    return (
        <div>
            <div>
                <Head>
                    <title>Masterdata Kode Produk</title>
                </Head>
            </div>
            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Sisko</Breadcrumb.Item>
                                    <Breadcrumb.Item>Kode Produk</Breadcrumb.Item>
                                    <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Halaman Masterdata Kode Produk</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Halaman Masterdata Kode Produk</h5>
                            </Stack>
                        }
                    ></Panel>
                    <div>
                        <Panel
                            bordered
                            bodyFill
                            header={
                                <Stack justifyContent="space-between">
                                    <div className="flex gap-2">
                                        <IconButton
                                            icon={<PlusRoundIcon />}
                                            appearance="primary"
                                            onClick={() => {
                                                setShowAddModal(true);
                                            }}
                                        >
                                            Tambah
                                        </IconButton>
                                    </div>
                                    <InputGroup inside>
                                        <InputGroup.Addon>
                                            <SearchIcon />
                                        </InputGroup.Addon>
                                        <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                                        <InputGroup.Addon
                                            onClick={() => {
                                                setSearchKeyword("");
                                                setPage(1);
                                            }}
                                            style={{
                                                display: searchKeyword ? "block" : "none",
                                                color: "red",
                                                cursor: "pointer",
                                            }}
                                        >
                                            <CloseOutlineIcon />
                                        </InputGroup.Addon>
                                    </InputGroup>
                                </Stack>
                            }
                        >

                            <Table
                                bordered
                                cellBordered
                                height={400}
                                data={getPaginatedData(getFilteredData(), limit, page)}
                                sortColumn={sortColumn}
                                sortType={sortType}
                                onSortColumn={handleSortColumn}

                            >
                                <Column width={70} align='center' fixed>
                                    <HeaderCell>No</HeaderCell>
                                    <Cell>
                                        {(_, index) => {
                                            return index + 1 + limit * (page - 1);
                                        }}
                                    </Cell>
                                </Column>
                                <Column width={150} sortable fullText resizable>
                                    <HeaderCell align="center">Kode Produk</HeaderCell>
                                    <Cell dataKey="kode_produk" />
                                </Column>

                                <Column width={120} sortable fullText resizable>
                                    <HeaderCell align="center">Bobot Min</HeaderCell>
                                    <Cell dataKey="bobot_min" />
                                </Column>

                                <Column width={120} sortable fullText resizable>
                                    <HeaderCell align="center">Bobot Max</HeaderCell>
                                    <Cell dataKey="bobot_max" />
                                </Column>

                                <Column width={120} sortable fullText resizable>
                                    <HeaderCell align="center">Bobot Std</HeaderCell>
                                    <Cell dataKey="bobot_std" />
                                </Column>

                                <Column width={150} sortable fullText resizable>
                                    <HeaderCell align="center">Bobot Core Foil</HeaderCell>
                                    <Cell dataKey="bobot_core_foil" />
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Pembuatan</HeaderCell>
                                    <Cell>{(rowData) => new Date(rowData.tanggal_dibuat).toLocaleDateString("en-GB")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Dibuat oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.dibuat_oleh}</>}</Cell>
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Diperbarui</HeaderCell>
                                    <Cell>{(rowData) => (rowData.tanggal_diubah ? new Date(rowData.tanggal_diubah).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.diubah_oleh}</>}</Cell>
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Dihapus</HeaderCell>
                                    <Cell>{(rowData) => (rowData.tanggal_dihapus ? new Date(rowData.tanggal_dihapus).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.dihapus_oleh}</>}</Cell>
                                </Column>
                                <Column width={100} sortable resizable align="center" fullText>
                                    <HeaderCell>Status</HeaderCell>
                                    <Cell>
                                        {(rowData) => (
                                            <span
                                                style={{
                                                    color: rowData.status === 1 ? "green" : "red",
                                                }}
                                            >
                                                {rowData.status === 1 ? "Aktif" : "Tidak Aktif"}
                                            </span>
                                        )}
                                    </Cell>
                                </Column>
                                <Column width={120} fixed="right" align="center">
                                    <HeaderCell>Aksi</HeaderCell>
                                    <Cell style={{ padding: "8px" }}>
                                        {(rowData) => (
                                            <div>
                                                <Button
                                                    appearance="subtle"
                                                    disabled={rowData.status === 0}
                                                    onClick={() => {
                                                        setShowEditModal(true);
                                                        setEditKodeProdukForm({
                                                            ...editKodeProdukForm,
                                                            id_produk: rowData.id_produk,
                                                            kode_produk: rowData.kode_produk,
                                                            bobot_min: rowData.bobot_min,
                                                            bobot_max: rowData.bobot_max,
                                                            bobot_std: rowData.bobot_std,
                                                            bobot_core_foil: rowData.bobot_core_foil,
                                                            diubah_oleh: user.no_karyawan + " - " + user.username,
                                                        });
                                                    }}
                                                >
                                                    <EditIcon />
                                                </Button>
                                                <Button appearance="subtle" onClick={() => handleEditStatusKodeProdukApi(rowData.id_produk, rowData.status)}>
                                                    {rowData.status === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                                                </Button>
                                            </div>
                                        )}
                                    </Cell>
                                </Column>
                            </Table>

                            <div style={{ padding: 20 }}>
                                <Pagination
                                    prev
                                    next
                                    first
                                    last
                                    ellipsis
                                    boundaryLinks
                                    maxButtons={5}
                                    size="xs"
                                    layout={["total", "-", "limit", "|", "pager", "skip"]}
                                    limitOptions={[10, 30, 50]}
                                    total={totalRowCount}
                                    limit={limit}
                                    activePage={page}
                                    onChangePage={setPage}
                                    onChangeLimit={handleChangeLimit}
                                />
                            </div>
                        </Panel>

                        <Modal
                            backdrop="static"
                            open={showAddModal}
                            onClose={() => {
                                setShowAddModal(false);
                                setAddKodeProdukForm(emptyAddKodeProdukForm);
                                setErrorsAddForm({});
                            }}
                            overflow={false}
                        >
                            <Modal.Header>
                                <Modal.Title>Tambah Kode Produk</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                <Form fluid>
                                    <Form.Group>
                                        <Form.ControlLabel>Kode Produk *</Form.ControlLabel>
                                        <Form.Control
                                            name="kode_produk"
                                            value={addKodeProdukForm.kode_produk}
                                            onChange={(value) => {
                                                setAddKodeProdukForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    kode_produk: value,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    kode_produk: undefined,
                                                }));
                                            }}
                                        />
                                        {errorsAddForm.kode_produk && <p style={{ color: "red" }}>{errorsAddForm.kode_produk}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Min</Form.ControlLabel>
                                        <InputNumber
                                            name="bobot_min"
                                            value={addKodeProdukForm.bobot_min}
                                            onChange={(value) => {
                                                setAddKodeProdukForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_min: parseFloat(value) || 0,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_min: undefined,
                                                }));
                                            }}

                                        />
                                        {errorsAddForm.bobot_min && <p style={{ color: "red" }}>{errorsAddForm.bobot_min}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Max</Form.ControlLabel>
                                        <InputNumber
                                            name="bobot_max"
                                            value={addKodeProdukForm.bobot_max}
                                            onChange={(value) => {
                                                setAddKodeProdukForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_max: parseFloat(value) || 0,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_max: undefined,
                                                }));
                                            }}

                                        />
                                        {errorsAddForm.bobot_max && <p style={{ color: "red" }}>{errorsAddForm.bobot_max}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Std</Form.ControlLabel>
                                        <InputNumber
                                            name="bobot_std"
                                            value={addKodeProdukForm.bobot_std}
                                            onChange={(value) => {
                                                setAddKodeProdukForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_std: parseFloat(value) || 0,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_std: undefined,
                                                }));
                                            }}

                                        />
                                        {errorsAddForm.bobot_std && <p style={{ color: "red" }}>{errorsAddForm.bobot_std}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Core Foil</Form.ControlLabel>
                                        <InputNumber
                                            name="bobot_core_foil"
                                            value={addKodeProdukForm.bobot_core_foil}
                                            onChange={(value) => {
                                                setAddKodeProdukForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_core_foil: parseFloat(value) || 0,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_core_foil: undefined,
                                                }));
                                            }}

                                        />
                                        {errorsAddForm.bobot_core_foil && <p style={{ color: "red" }}>{errorsAddForm.bobot_core_foil}</p>}
                                    </Form.Group>
                                </Form>
                            </Modal.Body>
                            <Modal.Footer>
                                <Button
                                    onClick={() => {
                                        setShowAddModal(false);
                                        setAddKodeProdukForm(emptyAddKodeProdukForm);
                                        setErrorsAddForm({});
                                    }}
                                    appearance="subtle"
                                >
                                    Batal
                                </Button>
                                <Button
                                    onClick={() => {
                                        HandleAddKodeProdukApi();
                                    }}
                                    appearance="primary"
                                    type="submit"
                                    loading={addLoading}
                                    disabled={addLoading}
                                >
                                    Tambah
                                </Button>

                            </Modal.Footer>
                        </Modal>
                        <Modal
                            backdrop="static"
                            open={showEditModal}
                            onClose={() => {
                                setShowEditModal(false);
                                setEditKodeProdukForm(emptyEditKodeProdukForm);
                                setErrorsEditForm({});
                            }}
                            overflow={false}
                        >
                            <Modal.Header>
                                <Modal.Title>Ubah Kode Produk</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                <Form fluid>
                                    <Form.Group>
                                        <Form.ControlLabel>Kode Produk *</Form.ControlLabel>
                                        <Form.Control
                                            name="kode_produk"
                                            value={editKodeProdukForm.kode_produk}
                                            onChange={(value) => {
                                                setEditKodeProdukForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    kode_produk: value,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    kode_produk: undefined,
                                                }));
                                            }}
                                        />
                                        {errorsEditForm.kode_produk && <p style={{ color: "red" }}>{errorsEditForm.kode_produk}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Min</Form.ControlLabel>
                                        <InputNumber
                                            name="bobot_min"
                                            value={editKodeProdukForm.bobot_min}
                                            onChange={(value) => {
                                                setEditKodeProdukForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_min: parseFloat(value) || 0,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_min: undefined,
                                                }));
                                            }}

                                        />
                                        {errorsEditForm.bobot_min && <p style={{ color: "red" }}>{errorsEditForm.bobot_min}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Max</Form.ControlLabel>
                                        <InputNumber
                                            name="bobot_max"
                                            value={editKodeProdukForm.bobot_max}
                                            onChange={(value) => {
                                                setEditKodeProdukForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_max: parseFloat(value) || 0,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_max: undefined,
                                                }));
                                            }}

                                        />
                                        {errorsEditForm.bobot_max && <p style={{ color: "red" }}>{errorsEditForm.bobot_max}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Std</Form.ControlLabel>
                                        <InputNumber
                                            name="bobot_std"
                                            value={editKodeProdukForm.bobot_std}
                                            onChange={(value) => {
                                                setEditKodeProdukForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_std: parseFloat(value) || 0,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_std: undefined,
                                                }));
                                            }}

                                        />
                                        {errorsEditForm.bobot_std && <p style={{ color: "red" }}>{errorsEditForm.bobot_std}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Core Foil</Form.ControlLabel>
                                        <InputNumber
                                            name="bobot_core_foil"
                                            value={editKodeProdukForm.bobot_core_foil}
                                            onChange={(value) => {
                                                setEditKodeProdukForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_core_foil: parseFloat(value) || 0,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_core_foil: undefined,
                                                }));
                                            }}

                                        />
                                        {errorsEditForm.bobot_core_foil && <p style={{ color: "red" }}>{errorsEditForm.bobot_core_foil}</p>}
                                    </Form.Group>
                                </Form>
                            </Modal.Body>
                            <Modal.Footer>
                                <Button
                                    onClick={() => {
                                        setShowEditModal(false);
                                        setEditKodeProdukForm(emptyEditKodeProdukForm);
                                        setErrorsEditForm({});
                                    }}
                                    appearance="subtle"
                                >
                                    Batal
                                </Button>
                                <Button
                                    onClick={() => {
                                        HandleEditKodeProdukApi();
                                    }}
                                    appearance="primary"
                                    type="submit"
                                    loading={addLoading}
                                    disabled={addLoading}
                                >
                                    Simpan
                                </Button>
                                {loading && <Loader backdrop size="md" vertical content="Editing Data..." active={loading} />}
                            </Modal.Footer>
                        </Modal>
                    </div>
                </div>
            </ContainerLayout>
        </div>
    );
}

export default withRoleAccess(MasterdataKodeProduk, rolePermissions['sisko/masterdata/kode_produk']);