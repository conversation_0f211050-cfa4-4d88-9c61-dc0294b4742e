import { useEffect, useState } from "react";
import Head from "next/head";
import {
    <PERSON>readcrumb,
    IconButton,
    Input,
    InputGroup,
    Pagination,
    Panel,
    Stack,
    Table,
    Tag,
    Button,
    Modal,
    Form,
    useToaster,
    ButtonGroup,
    Loader,
    DatePicker,
    RadioGroup,
    Radio,
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import ApiIndikator from "@/pages/api/sisko/masterdata_indikator/api_indikator";
import { useRouter } from "next/router";
import { useUser } from "@/context/UserContext";
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';

function MasterdataKodeProduk() {
    const { HeaderCell, Cell, Column } = Table;
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState("id_parameter");
    const [sortType, setSortType] = useState("asc");
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const router = useRouter();
    const [loading, setLoading] = useState(false);

    const { user, isAuthenticated, loading: userLoading } = useUser();

    const [moduleName, setModuleName] = useState("");
    const [IndikatorDataState, setIndikatorDataState] = useState([]);

    const emptyAddIndikatorForm = {
        nama_indikator: null,
        dapat_dikontrol: null,
        otomatis: null,
        satuan_pengukuran: null,
        tanggal_berlaku: null,
        tanggal_berakhir: null,
    };

    const emptyEditIndikatorForm = {
        nama_indikator: null,
        dapat_dikontrol: null,
        otomatis: null,
        satuan_pengukuran: null,
        tanggal_berlaku: null,
        tanggal_berakhir: null,
    };

    const [showAddModal, setShowAddModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [addIndikatorForm, setAddIndikatorForm] = useState(emptyAddIndikatorForm);
    const [editIndikatorForm, setEditIndikatorForm] = useState(emptyEditIndikatorForm);
    const [errorsAddForm, setErrorsAddForm] = useState({});
    const [errorsEditForm, setErrorsEditForm] = useState({});

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = IndikatorDataState.filter((rowData, i) => {
        const searchFields = ["id_indikator", "nama_indikator", "dapat_dikontrol", "satuan_pengukuran", "otomatis", "tanggal_berlaku", "tanggal_berakhir", "tanggal_dibuat", "dibuat_oleh", "tanggal_diubah", "diubah_oleh", "tanggal_dihapus", "dihapus_oleh", "status"];

        const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

        return matchesSearch;
    });

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    };

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : IndikatorDataState.length;

    useEffect(() => {
        if (userLoading) return;
        if (!isAuthenticated()) {
            router.push("/login");
            return;
        }
        // if (!user?.menu_link_code?.some((item) => item.includes("Indikator/masterdata"))) {
        //     router.push("/dashboard");
        //     return;
        // }
        setModuleName(user.module_name || "");
        HandleGetAllIndikatorApi();
    }, [userLoading]);

    const HandleGetAllIndikatorApi = async () => {
        try {
            const res = await ApiIndikator().getAllIndikator();

            console.log("res", res);
            if (res.status === 200) {
                setIndikatorDataState(res.data);
            } else {
                console.log("error on GetAllApi ", res.message);
            }
        } catch (error) {
            console.log("error on catch GetAllApi", error);
        }
    };

    const HandleAddIndikatorApi = async () => {
        const errors = {};

        if (!addIndikatorForm.nama_indikator) {
            errors.nama_indikator = "Nama Indikator wajib diisi";
        }

        if (!addIndikatorForm.satuan_pengukuran) {
            errors.satuan_pengukuran = "Nama Satuan Pengukuran wajib diisi";
        }

        if (!addIndikatorForm.tanggal_berlaku) {
            errors.tanggal_berlaku = "Tanggal Mulai wajib diisi";
        }

        if (!addIndikatorForm.tanggal_berakhir) {
            errors.tanggal_berakhir = "Tanggal Selesai wajib diisi";
        } else if (new Date(addIndikatorForm.tanggal_berakhir) < new Date(addIndikatorForm.tanggal_berlaku)) {
            errors.tanggal_berakhir = "Tanggal Selesai harus lebih besar dari Tanggal Mulai";
        }

        // If there are any errors, set them in the state
        if (Object.keys(errors).length > 0) {
            setErrorsAddForm(errors);
            return;
        }
        setLoading(true);
        try {
            const res = await ApiIndikator().createIndikator({
                ...addIndikatorForm,
                dibuat_oleh: user.no_karyawan + " - " + user.username,
            });

            if (res.status === 200) {
                setAddIndikatorForm(emptyAddIndikatorForm);
                setShowAddModal(false);
                HandleGetAllIndikatorApi();
            } else {
                console.log("error on AddIndikatorApi ", res.message);
            }
        } catch (error) {
            console.log("error on AddIndikatorApi ", error);
        } finally {
            setLoading(false);
        }
    };

    const HandleEditIndikatorApi = async () => {
        const errors = {};

        if (!editIndikatorForm.nama_indikator) {
            errors.nama_indikator = "Nama Indikator wajib diisi";
        }
        if (!editIndikatorForm.satuan_pengukuran) {
            errors.satuan_pengukuran = "Nama satuan pengukuran wajib diisi";
        }

        if (!editIndikatorForm.tanggal_berlaku) {
            errors.tanggal_berlaku = "Tanggal Mulai wajib diisi";
        }

        if (!editIndikatorForm.tanggal_berakhir) {
            errors.tanggal_berakhir = "Tanggal Berakhir name wajib diisi";
        } else if (new Date(editIndikatorForm.tanggal_berakhir) < new Date(editIndikatorForm.tanggal_berlaku)) {
            errors.tanggal_berakhir = "Tanggal Selesai harus lebih besar dari Tanggal Mulai";
        }


        // If there are any errors, set them in the state
        if (Object.keys(errors).length > 0) {
            setErrorsEditForm(errors);
            return;
        }
        try {
            const res = await ApiIndikator().editIndikator({
                ...editIndikatorForm,
                diubah_oleh: user.no_karyawan + " - " + user.username,
            });

            if (res.status === 200) {
                HandleGetAllIndikatorApi();
                setShowEditModal(false);
            } else {
                console.log("error on editIndikatorApi ", res.message);
            }
        } catch (error) {
            console.log("error on editIndikatorApi ", res.message);
        }
    };

    const handleEditStatusIndikatorApi = async (id_indikator, status) => {
        try {
            const res = await ApiIndikator().editStatusIndikator({
                id_indikator,
                status,
                dihapus_oleh: user.no_karyawan + " - " + user.username,
            });

            if (res.status === 200) {
                console.log("Status update success:", res.message);
                HandleGetAllIndikatorApi();
            } else {
                console.log("Error on update status: ", res.message);
            }
        } catch (error) {
            console.log("Error on update status: ", error.message);
        }
    };

    return (
        <div>
            <div>
                <Head>
                    <title>Parameter Indikator</title>
                </Head>
            </div>
            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Sisko</Breadcrumb.Item>
                                    <Breadcrumb.Item>Indikator</Breadcrumb.Item>
                                    <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Halaman Parameter Indikator</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Halaman Indikator</h5>
                            </Stack>
                        }
                    ></Panel>
                    <div>
                        <Panel
                            bordered
                            bodyFill
                            header={
                                <Stack justifyContent="space-between">
                                    <div className="flex gap-2">
                                        <IconButton
                                            icon={<PlusRoundIcon />}
                                            appearance="primary"
                                            onClick={() => {
                                                setShowAddModal(true);
                                            }}
                                        >
                                            Tambah
                                        </IconButton>
                                    </div>
                                    <InputGroup inside>
                                        <InputGroup.Addon>
                                            <SearchIcon />
                                        </InputGroup.Addon>
                                        <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                                        <InputGroup.Addon
                                            onClick={() => {
                                                setSearchKeyword("");
                                                setPage(1);
                                            }}
                                            style={{
                                                display: searchKeyword ? "block" : "none",
                                                color: "red",
                                                cursor: "pointer",
                                            }}
                                        >
                                            <CloseOutlineIcon />
                                        </InputGroup.Addon>
                                    </InputGroup>
                                </Stack>
                            }
                        >

                            <Table
                                bordered
                                cellBordered
                                height={400}
                                data={getPaginatedData(getFilteredData(), limit, page)}
                                sortColumn={sortColumn}
                                sortType={sortType}
                                onSortColumn={handleSortColumn}

                            >
                                <Column width={70} align='center' fixed>
                                    <HeaderCell>No</HeaderCell>
                                    <Cell>
                                        {(rowData, index) => {
                                            return index + 1 + limit * (page - 1);
                                        }}
                                    </Cell>
                                </Column>
                                <Column width={150} sortable fullText resizable>
                                    <HeaderCell align="center">Nama Indikator</HeaderCell>
                                    <Cell dataKey="nama_indikator" />
                                </Column>


                                <Column width={100} sortable fullText resizable>
                                    <HeaderCell align="center">Satuan Pengukuran</HeaderCell>
                                    <Cell dataKey="satuan_pengukuran" />
                                </Column>



                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Berlaku</HeaderCell>
                                    <Cell>{(rowData) => (rowData.tanggal_berlaku ? new Date(rowData.tanggal_berlaku).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>

                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Berakhir</HeaderCell>
                                    <Cell>{(rowData) => (rowData.tanggal_berakhir ? new Date(rowData.tanggal_berakhir).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Pembuatan</HeaderCell>
                                    <Cell>{(rowData) => new Date(rowData.tanggal_dibuat).toLocaleDateString("en-GB")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Dibuat oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.dibuat_oleh}</>}</Cell>
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Diperbarui</HeaderCell>
                                    <Cell>{(rowData) => (rowData.tanggal_diubah ? new Date(rowData.tanggal_diubah).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.diubah_oleh}</>}</Cell>
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Dihapus</HeaderCell>
                                    <Cell>{(rowData) => (rowData.tanggal_dihapus ? new Date(rowData.tanggal_dihapus).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.dihapus_oleh}</>}</Cell>
                                </Column>
                                <Column width={100} sortable resizable align="center" fullText>
                                    <HeaderCell>Status</HeaderCell>
                                    <Cell>
                                        {(rowData) => (
                                            <span
                                                style={{
                                                    color: rowData.status === 1 ? "green" : "red",
                                                }}
                                            >
                                                {rowData.status === 1 ? "Aktif" : "Tidak Aktif"}
                                            </span>
                                        )}
                                    </Cell>
                                </Column>
                                <Column width={120} fixed="right" align="center">
                                    <HeaderCell>Aksi</HeaderCell>
                                    <Cell style={{ padding: "8px" }}>
                                        {(rowData) => (
                                            <div>
                                                <Button
                                                    appearance="subtle"
                                                    disabled={rowData.status === 0}
                                                    onClick={() => {
                                                        setShowEditModal(true);
                                                        setEditIndikatorForm({
                                                            ...editIndikatorForm,
                                                            nama_indikator: rowData.nama_indikator,
                                                            dapat_dikontrol: rowData.dapat_dikontrol,
                                                            satuan_pengukuran: rowData.satuan_pengukuran,
                                                            otomatis: rowData.dapat_dikontrol === "N" ? "N" : rowData.otomatis,
                                                            tanggal_berlaku: rowData.tanggal_berlaku,
                                                            tanggal_berakhir: rowData.tanggal_berakhir,
                                                            id_indikator: rowData.id_indikator,
                                                            diubah_oleh: user.no_karyawan + " - " + user.username,
                                                        });
                                                    }}
                                                >
                                                    <EditIcon />
                                                </Button>
                                                <Button appearance="subtle" onClick={() => handleEditStatusIndikatorApi(rowData.id_indikator, rowData.status)}>
                                                    {rowData.status === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                                                </Button>
                                            </div>
                                        )}
                                    </Cell>
                                </Column>
                            </Table>

                            <div style={{ padding: 20 }}>
                                <Pagination
                                    prev
                                    next
                                    first
                                    last
                                    ellipsis
                                    boundaryLinks
                                    maxButtons={5}
                                    size="xs"
                                    layout={["total", "-", "limit", "|", "pager", "skip"]}
                                    limitOptions={[10, 30, 50]}
                                    total={totalRowCount}
                                    limit={limit}
                                    activePage={page}
                                    onChangePage={setPage}
                                    onChangeLimit={handleChangeLimit}
                                />
                            </div>
                        </Panel>

                        <Modal
                            backdrop="static"
                            open={showAddModal}
                            onClose={() => {
                                setShowAddModal(false);
                                setAddIndikatorForm(emptyAddIndikatorForm);
                                setErrorsAddForm({});
                            }}
                            overflow={false}
                        >
                            <Modal.Header>
                                <Modal.Title>Tambah Parameter</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                <Form fluid>
                                    <Form.Group>
                                        <Form.ControlLabel>Nama Indikator</Form.ControlLabel>
                                        <Form.Control
                                            name="nama_indikator"
                                            value={addIndikatorForm.nama_indikator}
                                            onChange={(value) => {
                                                setAddIndikatorForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    nama_indikator: value,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    nama_indikator: undefined,
                                                }));
                                            }}
                                        />
                                        {errorsAddForm.nama_indikator && <p style={{ color: "red" }}>{errorsAddForm.nama_indikator}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Satuan Pengukuran</Form.ControlLabel>
                                        <Form.Control
                                            name="satuan_pengukuran"
                                            value={addIndikatorForm.satuan_pengukuran}
                                            onChange={(value) => {
                                                setAddIndikatorForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    satuan_pengukuran: value,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    satuan_pengukuran: undefined,
                                                }));
                                            }}
                                        />
                                        {errorsAddForm.satuan_pengukuran && <p style={{ color: "red" }}>{errorsAddForm.satuan_pengukuran}</p>}
                                    </Form.Group>
                                    {/* <Form.Group>
                    <Form.ControlLabel>Flag Controllable</Form.ControlLabel>
                    <RadioGroup
                      name="flag_controllable"
                      inline
                      value={addIndikatorForm.flag_controllable}
                      onChange={(value) => {
                        setAddIndikatorForm((prevFormValue) => ({
                          ...prevFormValue,
                          flag_controllable: value,
                          // Jika flag controllable diubah menjadi "N", set is_automate menjadi "N"
                          is_automate: value === "N" ? "N" : prevFormValue.is_automate,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          flag_controllable: undefined,
                        }));
                      }}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsAddForm.flag_controllable && <p style={{ color: "red" }}>{errorsAddForm.flag_controllable}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Is Automate</Form.ControlLabel>
                    <RadioGroup
                      name="is_automate"
                      inline
                      value={addIndikatorForm.is_automate}
                      onChange={(value) => {
                        setAddIndikatorForm((prevFormValue) => ({
                          ...prevFormValue,
                          is_automate: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          is_automate: undefined,
                        }));
                      }}
                      disabled={addIndikatorForm.flag_controllable === "N"}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsAddForm.is_automate && <p style={{ color: "red" }}>{errorsAddForm.is_automate}</p>}
                  </Form.Group> */}
                                    <Form.Group>
                                        <Form.ControlLabel>Tanggal Mulai</Form.ControlLabel>
                                        <DatePicker
                                            name="tanggal_berlaku"
                                            placeholder="Pilih Tanggal Mulai"
                                            value={addIndikatorForm.tanggal_berlaku}
                                            onChange={(value) => {
                                                setAddIndikatorForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    tanggal_berlaku: value,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    tanggal_berlaku: undefined,
                                                }));
                                            }}
                                            block
                                        />
                                        {errorsAddForm.tanggal_berlaku && <p style={{ color: "red" }}>{errorsAddForm.tanggal_berlaku}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Tanggal Selesai</Form.ControlLabel>
                                        <DatePicker
                                            name="tanggal_berakhir"
                                            placeholder="Pilih Tanggal Selesai"
                                            value={addIndikatorForm.tanggal_berakhir}
                                            onChange={(value) => {
                                                setAddIndikatorForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    tanggal_berakhir: value,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    tanggal_berakhir: undefined,
                                                }));
                                            }}
                                            block
                                        />
                                        {errorsAddForm.tanggal_berakhir && <p style={{ color: "red" }}>{errorsAddForm.tanggal_berakhir}</p>}
                                    </Form.Group>
                                </Form>
                            </Modal.Body>
                            <Modal.Footer>
                                <Button
                                    onClick={() => {
                                        setShowAddModal(false);
                                        setAddIndikatorForm(emptyAddIndikatorForm);
                                        setErrorsAddForm({});
                                    }}
                                    appearance="subtle"
                                >
                                    Batal
                                </Button>
                                <Button
                                    onClick={() => {
                                        HandleAddIndikatorApi();
                                    }}
                                    appearance="primary"
                                    type="submit"
                                    loading={loading} // Menampilkan loading state pada tombol
                                    disabled={loading} // Menonaktifkan tombol saat loading
                                >
                                    Tambah
                                </Button>
                                {loading && <Loader backdrop size="md" vertical content="Adding Data..." active={loading} />}
                            </Modal.Footer>
                        </Modal>
                        <Modal
                            backdrop="static"
                            open={showEditModal}
                            onClose={() => {
                                setShowEditModal(false);
                                setEditIndikatorForm(emptyEditIndikatorForm);
                                setErrorsEditForm({});
                            }}
                            overflow={false}
                        >
                            <Modal.Header>
                                <Modal.Title>Ubah Indikator</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                <Form fluid>
                                    <Form.Group>
                                        <Form.ControlLabel>Nama Indikator</Form.ControlLabel>
                                        <Form.Control
                                            name="nama_indikator"
                                            value={editIndikatorForm.nama_indikator}
                                            onChange={(value) => {
                                                setEditIndikatorForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    nama_indikator: value,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    nama_indikator: undefined,
                                                }));
                                            }}
                                        />
                                        {errorsEditForm.nama_indikator && <p style={{ color: "red" }}>{errorsEditForm.nama_indikator}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Satuan Pengukuran</Form.ControlLabel>
                                        <Form.Control
                                            name="satuan_pengukuran"
                                            value={editIndikatorForm.satuan_pengukuran}
                                            onChange={(value) => {
                                                setEditIndikatorForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    satuan_pengukuran: value,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    satuan_pengukuran: undefined,
                                                }));
                                            }}
                                        />
                                        {errorsEditForm.satuan_pengukuran && <p style={{ color: "red" }}>{errorsEditForm.satuan_pengukuran}</p>}
                                    </Form.Group>
                                    {/* <Form.Group>
                    <Form.ControlLabel>Flag Controllable</Form.ControlLabel>
                    <RadioGroup
                      name="flag_controllable"
                      inline
                      value={editIndikatorForm.flag_controllable}
                      onChange={(value) => {
                        setEditIndikatorForm((prevFormValue) => ({
                          ...prevFormValue,
                          flag_controllable: value,
                          // Jika flag controllable diubah menjadi "N", set is_automate menjadi "N"
                          is_automate: value === "N" ? "N" : prevFormValue.is_automate,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          flag_controllable: undefined,
                        }));
                      }}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsEditForm.flag_controllable && <p style={{ color: "red" }}>{errorsEditForm.flag_controllable}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Is Automate</Form.ControlLabel>
                    <RadioGroup
                      name="is_automate"
                      inline
                      value={editIndikatorForm.is_automate}
                      onChange={(value) => {
                        setEditIndikatorForm((prevFormValue) => ({
                          ...prevFormValue,
                          is_automate: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          is_automate: undefined,
                        }));
                      }}
                      disabled={editIndikatorForm.flag_controllable === "N"}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsEditForm.is_automate && <p style={{ color: "red" }}>{errorsEditForm.is_automate}</p>}
                  </Form.Group> */}
                                    <Form.Group>
                                        <Form.ControlLabel>Tanggal Mulai</Form.ControlLabel>
                                        <DatePicker
                                            format="dd/MM/yyyy"
                                            value={editIndikatorForm.tanggal_berlaku ? new Date(editIndikatorForm.tanggal_berlaku) : null}
                                            onChange={(value) => {
                                                setEditIndikatorForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    tanggal_berlaku: value ? value.toISOString().split("T")[0] : null,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    tanggal_berlaku: undefined,
                                                }));
                                            }}
                                            block
                                        />
                                        {errorsEditForm.tanggal_berlaku && <p style={{ color: "red" }}>{errorsEditForm.tanggal_berlaku}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Tanggal Selesai</Form.ControlLabel>
                                        <DatePicker
                                            format="dd/MM/yyyy"
                                            value={editIndikatorForm.tanggal_berakhir ? new Date(editIndikatorForm.tanggal_berakhir) : null}
                                            onChange={(value) => {
                                                setEditIndikatorForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    tanggal_berakhir: value ? value.toISOString().split("T")[0] : null,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    tanggal_berakhir: undefined,
                                                }));
                                            }}
                                            block
                                        />
                                        {errorsEditForm.tanggal_berakhir && <p style={{ color: "red" }}>{errorsEditForm.tanggal_berakhir}</p>}
                                    </Form.Group>
                                </Form>
                            </Modal.Body>
                            <Modal.Footer>
                                <Button
                                    onClick={() => {
                                        setShowEditModal(false);
                                        setEditIndikatorForm(emptyEditIndikatorForm);
                                        setErrorsEditForm({});
                                    }}
                                    appearance="subtle"
                                >
                                    Batal
                                </Button>
                                <Button
                                    onClick={() => {
                                        HandleEditIndikatorApi();
                                    }}
                                    appearance="primary"
                                    type="submit"
                                >
                                    Simpan
                                </Button>
                                {loading && <Loader backdrop size="md" vertical content="Editing Data..." active={loading} />}
                            </Modal.Footer>
                        </Modal>
                    </div>
                </div>
            </ContainerLayout>
        </div>
    );
}

export default withRoleAccess(MasterdataKodeProduk, rolePermissions['sisko/masterdata/kode_produk']);