// Role permission mapping for the entire application
const rolePermissions = {
  // Dashboard - accessible by all roles
  'dashboard': [1, 2, 3, 4],
  
  // User Profile - accessible by all roles
  'user_profile': [1, 2, 3, 4],
  
  // Operator Transaksi - accessible by role 1 (admin) and 4 (operator)
  'sisko/operator/creation': [1, 4],
  'sisko/operator/creation/addTransactionHeader': [1, 4],
  'sisko/operator/creation/list': [1, 4],
  'sisko/operator/creation/detail': [1, 4],
  'sisko/operator/reporting': [1, 4],
  
  // Quality Control - accessible by role 1 (admin) and 2 (quality control)
  'sisko/masterdata/ppr': [1, 2],
  'sisko/masterdata/ppr/resep': [1, 2],
  'sisko/masterdata/ppr/detailResep': [1, 2, 3],
  'sisko/masterdata/langkah': [1, 2],
  'sisko/masterdata/indikator': [1, 2],
  'sisko/masterdata/kode_produk': [1, 2],
  
  // User Management - accessible by role 1 (admin) only
  'masterdata-users': [1],
  'masterdata-roles': [1],
  
  // Approval Supervisor - accessible by role 1 (admin) and 3 (supervisor)
  'sisko/approval/ppr': [1, 3],
  'sisko/approval/ppr/resep': [1, 3],
  'sisko/approval/transaksi': [1, 3],
  'sisko/approval/transaksi/list': [1, 3],
  'sisko/approval/reporting': [1, 3],
};

export default rolePermissions;